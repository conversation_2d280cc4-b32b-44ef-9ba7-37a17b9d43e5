# Apache .htaccess configuration for SEO and performance
# Copy this file to your web root when deploying to Apache servers

# Security Headers
<IfModule mod_headers.c>
    Header always set X-DNS-Prefetch-Control "on"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "origin-when-cross-origin"
</IfModule>

# Cache Control
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Cache static assets for 1 year
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/eot "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    
    # Cache HTML for 1 hour
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Redirects
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirect common variations to canonical URLs
    RewriteRule ^home/?$ / [R=301,L]
    RewriteRule ^index/?$ / [R=301,L]
    RewriteRule ^privacy/?$ /privacy-policy [R=301,L]
    RewriteRule ^terms/?$ /terms-of-service [R=301,L]
    RewriteRule ^tos/?$ /terms-of-service [R=301,L]
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
