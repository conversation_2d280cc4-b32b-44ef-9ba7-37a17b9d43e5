# Headers for Netlify hosting
# These headers will be applied when deployed to Netlify

# Security and performance headers for all pages
/*
  X-DNS-Prefetch-Control: on
  X-XSS-Protection: 1; mode=block
  X-Frame-Options: SAMEORIGIN
  X-Content-Type-Options: nosniff
  Referrer-Policy: origin-when-cross-origin

# Cache static assets for 1 year
/*.ico
  Cache-Control: public, max-age=31536000, immutable
/*.png
  Cache-Control: public, max-age=31536000, immutable
/*.jpg
  Cache-Control: public, max-age=31536000, immutable
/*.jpeg
  Cache-Control: public, max-age=31536000, immutable
/*.gif
  Cache-Control: public, max-age=31536000, immutable
/*.webp
  Cache-Control: public, max-age=31536000, immutable
/*.svg
  Cache-Control: public, max-age=31536000, immutable
/*.woff
  Cache-Control: public, max-age=31536000, immutable
/*.woff2
  Cache-Control: public, max-age=31536000, immutable
/*.ttf
  Cache-Control: public, max-age=31536000, immutable
/*.eot
  Cache-Control: public, max-age=31536000, immutable
/*.otf
  Cache-Control: public, max-age=31536000, immutable

# Cache HTML pages for 1 hour
/*.html
  Cache-Control: public, max-age=3600, s-maxage=3600
