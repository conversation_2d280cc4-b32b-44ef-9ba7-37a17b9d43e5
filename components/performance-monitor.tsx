/**
 * Performance monitoring component for Core Web Vitals
 */

"use client"

import { useEffect } from "react"

interface PerformanceMetric {
  name: string
  value: number
  rating: "good" | "needs-improvement" | "poor"
}

export function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production and when performance API is available
    if (process.env.NODE_ENV !== "production" || typeof window === "undefined") {
      return
    }

    // Core Web Vitals thresholds
    const thresholds = {
      LCP: { good: 2500, poor: 4000 }, // Largest Contentful Paint
      FID: { good: 100, poor: 300 },   // First Input Delay
      CLS: { good: 0.1, poor: 0.25 },  // Cumulative Layout Shift
      FCP: { good: 1800, poor: 3000 }, // First Contentful Paint
      TTFB: { good: 800, poor: 1800 }, // Time to First Byte
    }

    function getRating(value: number, metric: keyof typeof thresholds): "good" | "needs-improvement" | "poor" {
      const threshold = thresholds[metric]
      if (value <= threshold.good) return "good"
      if (value <= threshold.poor) return "needs-improvement"
      return "poor"
    }

    function reportMetric(metric: PerformanceMetric) {
      // In production, you would send this to your analytics service
      console.log(`[Performance] ${metric.name}: ${metric.value}ms (${metric.rating})`)
      
      // Example: Send to Google Analytics 4
      if (typeof gtag !== "undefined") {
        gtag("event", metric.name, {
          event_category: "Web Vitals",
          value: Math.round(metric.value),
          custom_parameter_1: metric.rating,
        })
      }

      // Example: Send to custom analytics endpoint
      if (process.env.NODE_ENV === "production") {
        fetch("/api/analytics/performance", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(metric),
        }).catch(() => {
          // Silently fail - don't impact user experience
        })
      }
    }

    // Measure LCP (Largest Contentful Paint)
    function measureLCP() {
      if ("PerformanceObserver" in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1] as PerformanceEntry & { startTime: number }
          
          reportMetric({
            name: "LCP",
            value: lastEntry.startTime,
            rating: getRating(lastEntry.startTime, "LCP"),
          })
        })
        
        observer.observe({ entryTypes: ["largest-contentful-paint"] })
      }
    }

    // Measure FID (First Input Delay)
    function measureFID() {
      if ("PerformanceObserver" in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            reportMetric({
              name: "FID",
              value: entry.processingStart - entry.startTime,
              rating: getRating(entry.processingStart - entry.startTime, "FID"),
            })
          })
        })
        
        observer.observe({ entryTypes: ["first-input"] })
      }
    }

    // Measure CLS (Cumulative Layout Shift)
    function measureCLS() {
      if ("PerformanceObserver" in window) {
        let clsValue = 0
        
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          
          reportMetric({
            name: "CLS",
            value: clsValue,
            rating: getRating(clsValue, "CLS"),
          })
        })
        
        observer.observe({ entryTypes: ["layout-shift"] })
      }
    }

    // Measure FCP (First Contentful Paint)
    function measureFCP() {
      if ("PerformanceObserver" in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            reportMetric({
              name: "FCP",
              value: entry.startTime,
              rating: getRating(entry.startTime, "FCP"),
            })
          })
        })
        
        observer.observe({ entryTypes: ["paint"] })
      }
    }

    // Measure TTFB (Time to First Byte)
    function measureTTFB() {
      if ("performance" in window && "timing" in performance) {
        const timing = performance.timing
        const ttfb = timing.responseStart - timing.navigationStart
        
        reportMetric({
          name: "TTFB",
          value: ttfb,
          rating: getRating(ttfb, "TTFB"),
        })
      }
    }

    // Initialize measurements
    measureLCP()
    measureFID()
    measureCLS()
    measureFCP()
    measureTTFB()

    // Cleanup function
    return () => {
      // Observers are automatically cleaned up when the component unmounts
    }
  }, [])

  // This component doesn't render anything
  return null
}

// Hook for manual performance measurements
export function usePerformanceMetric(name: string) {
  return {
    start: () => {
      if (typeof window !== "undefined" && "performance" in window) {
        performance.mark(`${name}-start`)
      }
    },
    end: () => {
      if (typeof window !== "undefined" && "performance" in window) {
        performance.mark(`${name}-end`)
        performance.measure(name, `${name}-start`, `${name}-end`)
        
        const measure = performance.getEntriesByName(name)[0]
        if (measure) {
          console.log(`[Performance] ${name}: ${measure.duration}ms`)
        }
      }
    },
  }
}
