{"headers": [{"source": "/(.*)", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}]}, {"source": "/(.*\\.(ico|png|jpg|jpeg|gif|webp|svg|woff|woff2|ttf|eot|otf))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.html)", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=3600"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/index", "destination": "/", "permanent": true}, {"source": "/privacy", "destination": "/privacy-policy", "permanent": true}, {"source": "/terms", "destination": "/terms-of-service", "permanent": true}, {"source": "/tos", "destination": "/terms-of-service", "permanent": true}]}